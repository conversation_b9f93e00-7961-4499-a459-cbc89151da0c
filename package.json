{"name": "knowladge-base-solution", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@react-three/drei": "^10.6.1", "@react-three/fiber": "^9.3.0", "@stianlarsen/border-beam": "^1.0.11", "@tanstack/react-table": "^8.21.3", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "@types/three": "^0.178.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.3", "lottie-react": "^2.4.1", "lucide-react": "^0.515.0", "motion": "^12.23.0", "next": "15.3.3", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "ogl": "^1.0.11", "prism-react-renderer": "^2.4.1", "prop-types": "^15.8.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.62.0", "react-icons": "^5.5.0", "react-joyride": "^3.0.0-7", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "reactflow": "^11.11.4", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "three": "^0.178.0", "zod": "^4.0.14", "zustand": "^4.4.1"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/prop-types": "^15.7.15", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-table": "^7.7.20", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}