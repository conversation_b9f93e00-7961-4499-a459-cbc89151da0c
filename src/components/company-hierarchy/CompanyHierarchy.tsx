"use client";

import { useState } from "react";
import { CompanyTreeView } from "./CompanyTreeView";

export interface Company {
  id: string;
  name: string;
  description: string;
  contactDatabase: string;
  children?: Company[];
  parentId?: string;
}

export function CompanyHierarchy() {
  const [companies, setCompanies] = useState<Company[]>([
    {
      id: "1",
      name: "Meta",
      description:
        "Automate refund processes with configurable policy enforcement.",
      contactDatabase: "meta_db",
      children: [
        {
          id: "2",
          name: "Facebook",
          description:
            "Automate refund processes with configurable policy enforcement.",
          contactDatabase: "facebook_db",
          parentId: "1",
        },
        {
          id: "3",
          name: "Instagram",
          description:
            "Automate refund processes with configurable policy enforcement.",
          contactDatabase: "instagram_db",
          parentId: "1",
        },
        {
          id: "4",
          name: "Thread",
          description:
            "Automate refund processes with configurable policy enforcement.",
          contactDatabase: "thread_db",
          parentId: "1",
        },
      ],
    },
  ]);

  const [selectedCompany, setSelectedCompany] = useState<string | null>(null);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      <CompanyTreeView
        companies={companies}
        selectedCompany={selectedCompany}
        onSelectCompany={setSelectedCompany}
      />
    </div>
  );
}
