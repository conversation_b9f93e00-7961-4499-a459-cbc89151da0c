"use client";

import { Building2, Upload, Plus, ChevronDown, ChevronRight } from "lucide-react";
import { useState, useEffect } from "react";
import { Company } from "./CompanyHierarchy";
import { JSX } from "react/jsx-runtime";

interface CompanyTreeViewProps {
  companies: Company[];
  selectedCompany: string | null;
  onSelectCompany: (id: string | null) => void;
}

interface NodePosition {
  x: number;
  y: number;
  id: string;
  company: Company;
  level: number;
}

export function CompanyTreeView({
  companies,
  selectedCompany,
  onSelectCompany,
}: CompanyTreeViewProps) {
  const [nodePositions, setNodePositions] = useState<NodePosition[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedParentId, setSelectedParentId] = useState<string | null>(null);
  const [newCompanyData, setNewCompanyData] = useState({ name: '', description: '', contactDatabase: '' });

  // Calculate node positions to match the image layout
  useEffect(() => {
    const positions: NodePosition[] = [];

    if (companies.length === 0) {
      setNodePositions([]);
      return;
    }

    // Position Meta at the center-left area
    const mainCompany = companies[0];
    const centerX = 400;
    const centerY = 300;

    positions.push({
      x: centerX,
      y: centerY,
      id: mainCompany.id,
      company: mainCompany,
      level: 0,
    });

    // Position children below Meta in a horizontal line
    if (mainCompany.children && mainCompany.children.length > 0) {
      const childY = centerY + 280;
      const spacing = 320;
      const startX = centerX - (mainCompany.children.length - 1) * spacing / 2;

      mainCompany.children.forEach((child, index) => {
        positions.push({
          x: startX + index * spacing,
          y: childY,
          id: child.id,
          company: child,
          level: 1,
        });
      });
    }

    setNodePositions(positions);
  }, [companies]);

  const renderConnections = () => {
    const connections: JSX.Element[] = [];

    nodePositions.forEach((node) => {
      if (node.company.children) {
        node.company.children.forEach((child) => {
          const childNode = nodePositions.find((n) => n.id === child.id);
          if (childNode) {
            const startX = node.x;
            const startY = node.y + 120; // Bottom of parent card
            const endX = childNode.x;
            const endY = childNode.y - 120; // Top of child card

            // Vertical line down from parent
            const midY = startY + (endY - startY) / 2;

            connections.push(
              <g key={`connection-${node.id}-${child.id}`}>
                {/* Vertical line from parent */}
                <line
                  x1={startX}
                  y1={startY}
                  x2={startX}
                  y2={midY}
                  stroke="rgba(34, 197, 94, 0.6)"
                  strokeWidth="2"
                />

                {/* Horizontal line to child position */}
                <line
                  x1={startX}
                  y1={midY}
                  x2={endX}
                  y2={midY}
                  stroke="rgba(34, 197, 94, 0.6)"
                  strokeWidth="2"
                />

                {/* Vertical line to child */}
                <line
                  x1={endX}
                  y1={midY}
                  x2={endX}
                  y2={endY}
                  stroke="rgba(34, 197, 94, 0.6)"
                  strokeWidth="2"
                />

                {/* Connection dots */}
                <circle cx={startX} cy={startY} r="3" fill="#22c55e" className="animate-pulse" />
                <circle cx={startX} cy={midY} r="3" fill="#22c55e" className="animate-pulse" style={{ animationDelay: "0.3s" }} />
                <circle cx={endX} cy={midY} r="3" fill="#22c55e" className="animate-pulse" style={{ animationDelay: "0.6s" }} />
                <circle cx={endX} cy={endY} r="3" fill="#22c55e" className="animate-pulse" style={{ animationDelay: "0.9s" }} />
              </g>
            );
          }
        });
      }
    });

    return connections;
  };

  const renderFloatingDots = () => {
    const dots: JSX.Element[] = [];
    for (let i = 0; i < 15; i++) {
      // Reduce number and make them more subtle
      dots.push(
        <div
          key={i}
          className="absolute w-0.5 h-0.5 bg-green-400/40 rounded-full animate-pulse"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animationDelay: `${Math.random() * 3}s`,
            animationDuration: `${3 + Math.random() * 2}s`,
          }}
        />
      );
    }
    return dots;
  };

  // Show loading state if no companies
  if (companies.length === 0) {
    return (
      <div
        className="min-h-screen w-full relative overflow-hidden flex items-center justify-center"
        style={{
          background: "linear-gradient(135deg, #FFFFFF 0%, #858585 100%)",
        }}
      >
        <div className="text-white text-xl">Loading companies...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full relative overflow-hidden bg-gradient-to-br from-gray-900 via-black to-gray-800">
      {/* Grid Background - Dot Matrix Style like in the image */}
      <div className="absolute inset-0">
        {/* Main dot grid pattern */}
        <div
          className="absolute inset-0 opacity-25"
          style={{
            backgroundImage: `
              radial-gradient(circle at 1px 1px, rgba(34, 197, 94, 0.4) 1px, transparent 0)
            `,
            backgroundSize: "40px 40px",
          }}
        />

        {/* Secondary smaller dots for detail */}
        <div
          className="absolute inset-0 opacity-15"
          style={{
            backgroundImage: `
              radial-gradient(circle at 1px 1px, rgba(34, 197, 94, 0.3) 0.5px, transparent 0)
            `,
            backgroundSize: "20px 20px",
          }}
        />

        {/* Subtle line grid overlay */}
        <div
          className="absolute inset-0 opacity-8"
          style={{
            backgroundImage: `
              linear-gradient(rgba(34, 197, 94, 0.1) 0.5px, transparent 0.5px),
              linear-gradient(90deg, rgba(34, 197, 94, 0.1) 0.5px, transparent 0.5px)
            `,
            backgroundSize: "40px 40px",
          }}
        />
      </div>

      {/* Floating dots */}
      {renderFloatingDots()}

      {/* SVG for connections */}
      <svg className="absolute inset-0 w-full h-full pointer-events-none">
        <defs>
          <filter id="glow">
            <feGaussianBlur stdDeviation="3" result="coloredBlur" />
            <feMerge>
              <feMergeNode in="coloredBlur" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>
        {renderConnections()}
      </svg>

      {/* Company Nodes */}
      <div className="relative w-full h-full">
        {nodePositions.map((node) => {
          const isSelected = selectedCompany === node.id;
          const isMainCompany = node.level === 0;

          return (
            <div key={node.id} className="relative">
              {/* Large rectangular border for Meta */}
              {isMainCompany && (
                <div
                  className="absolute transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
                  style={{
                    left: node.x,
                    top: node.y,
                    width: '450px',
                    height: '280px',
                    zIndex: 5,
                  }}
                >
                  <div className="w-full h-full rounded-3xl border-2 border-green-400/60 bg-transparent animate-pulse" />
                </div>
              )}

              {/* Plus button at top */}
              {isMainCompany && (
                <div
                  className="absolute transform -translate-x-1/2 -translate-y-1/2 z-20"
                  style={{
                    left: node.x,
                    top: node.y - 140,
                  }}
                >
                  <div className="w-8 h-8 rounded-full bg-green-500/20 border-2 border-green-400 flex items-center justify-center cursor-pointer hover:bg-green-500/30 transition-colors">
                    <Plus className="w-4 h-4 text-green-400" />
                  </div>
                </div>
              )}

              {/* Custom Company Card matching SystemCard design */}
              <div
                className="absolute transform -translate-x-1/2 -translate-y-1/2 pointer-events-auto cursor-pointer group"
                style={{
                  left: node.x,
                  top: node.y,
                  zIndex: isSelected ? 1000 : 10,
                  transition: "all 0.3s ease-out",
                }}
                onClick={() => onSelectCompany(node.id)}
              >
                <div
                  className={`relative group transition-all duration-300 ${
                    isSelected ? "scale-105" : "hover:scale-105"
                  }`}
                  style={{
                    filter: isSelected ? "drop-shadow(0 0 30px rgba(16, 185, 129, 0.6))" : undefined,
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.filter = "drop-shadow(0 0 40px rgba(16, 185, 129, 0.8))";
                  }}
                  onMouseLeave={(e) => {
                    if (!isSelected) {
                      e.currentTarget.style.filter = "";
                    }
                  }}
                >
                  {/* Glass Card matching SystemCard design */}
                  <div
                    className="relative overflow-hidden"
                    style={{
                      width: "371.2px",
                      height: "200px",
                      borderRadius: "25.6px",
                      background: "rgba(255, 255, 255, 0.03)",
                      border: "1.2px solid transparent",
                      backgroundImage: "linear-gradient(158.39deg, rgba(16, 185, 129, 0.08) 14.19%, rgba(16, 185, 129, 0.03) 50.59%, rgba(16, 185, 129, 0.08) 68.79%, rgba(16, 185, 129, 0.03) 105.18%)",
                      backdropFilter: "blur(20px)",
                      boxShadow: "0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)",
                    }}
                  >
                    {/* Animated glow dots in corners */}
                    <div className="absolute top-4 left-4 w-1 h-1 bg-emerald-400 rounded-full animate-pulse opacity-60"
                         style={{ boxShadow: "0 0 8px #10b981" }} />
                    <div className="absolute top-8 left-2 w-0.5 h-0.5 bg-emerald-300 rounded-full animate-pulse opacity-40"
                         style={{ boxShadow: "0 0 4px #6ee7b7" }} />
                    <div className="absolute bottom-12 left-6 w-0.5 h-0.5 bg-emerald-400 rounded-full animate-pulse opacity-50"
                         style={{ boxShadow: "0 0 6px #10b981" }} />
                    <div className="absolute bottom-4 left-2 w-1 h-1 bg-emerald-300 rounded-full animate-pulse opacity-30"
                         style={{ boxShadow: "0 0 8px #6ee7b7" }} />

                    {/* Main content layout */}
                    <div className="relative z-10 h-full flex items-center gap-6">
                      {/* Left side - Circular icon area */}
                      <div className="flex-shrink-0">
                        {/* Outer glow ring */}
                        <div
                          className="relative w-24 h-24 rounded-full border border-emerald-400/30"
                          style={{
                            background: "radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, transparent 70%)",
                            boxShadow: "0 0 20px rgba(16, 185, 129, 0.2), inset 0 0 20px rgba(16, 185, 129, 0.1)"
                          }}
                        >
                          {/* Inner icon container */}
                          <div className="absolute inset-2 rounded-full bg-emerald-600 flex items-center justify-center"
                               style={{
                                 background: "linear-gradient(135deg, #10b981 0%, #059669 100%)",
                                 boxShadow: "0 0 15px rgba(16, 185, 129, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.1)"
                               }}>
                            {/* API text */}
                            <span className="text-white font-bold text-sm tracking-wide">API</span>

                            {/* Gear icon on top */}
                            <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="text-emerald-300">
                                <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5a3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97c0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1c0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z" fill="currentColor"/>
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Right side - Text content */}
                      <div className="flex-1">
                        <h2 className="text-2xl font-semibold mb-3 tracking-wide text-white">
                          {node.company.name}
                        </h2>
                        <p className="text-base leading-relaxed text-gray-300">
                          {node.company.description || "Automate refund processes with configurable policy enforcement."}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Plus button at bottom */}
              <div
                className="absolute transform -translate-x-1/2 -translate-y-1/2 z-20"
                style={{
                  left: node.x,
                  top: node.y + 140,
                }}
              >
                <div className="w-8 h-8 rounded-full bg-green-500/20 border-2 border-green-400 flex items-center justify-center cursor-pointer hover:bg-green-500/30 transition-colors">
                  <Plus className="w-4 h-4 text-green-400" />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Right sidebar with company tree */}
      <div className="absolute top-4 right-4 w-80 bg-gray-900/90 backdrop-blur-md border border-green-500/30 rounded-2xl p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-lg bg-green-500/20 flex items-center justify-center">
              <Building2 className="w-4 h-4 text-green-400" />
            </div>
            <h3 className="text-white font-semibold text-lg">Meta</h3>
          </div>
          <ChevronDown className="w-4 h-4 text-green-400" />
        </div>

        <div className="space-y-3">
          {companies.map((company) => (
            <div key={company.id}>
              {/* Parent company */}
              <div className="flex items-center gap-3 text-sm">
                <div className="w-4 h-4 rounded border border-green-500/40 bg-green-500/10 flex items-center justify-center">
                  <div className="w-2 h-2 rounded-full bg-green-400" />
                </div>
                <div className="w-2 h-2 rounded-full bg-green-400" />
                <span className="text-white font-medium">{company.name}</span>
              </div>

              {/* Children */}
              {company.children &&
                company.children.map((child, index) => (
                  <div
                    key={child.id}
                    className="flex items-center gap-3 text-sm ml-6 mt-2"
                  >
                    <div className="w-4 h-4 rounded border border-green-500/30 bg-transparent flex items-center justify-center">
                      {/* Empty checkbox for children */}
                    </div>
                    <div className="w-2 h-2 rounded-full bg-green-400/60" />
                    <span className="text-gray-300">{child.name}</span>
                  </div>
                ))}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
