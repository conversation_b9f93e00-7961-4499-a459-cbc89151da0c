"use client";

import { Building2, Upload, Plus, ChevronDown } from "lucide-react";
import { useState, useEffect } from "react";
import { Company } from "./CompanyHierarchy";
import { JSX } from "react/jsx-runtime";

interface CompanyTreeViewProps {
  companies: Company[];
  selectedCompany: string | null;
  onSelectCompany: (id: string | null) => void;
}

interface NodePosition {
  x: number;
  y: number;
  id: string;
  company: Company;
  level: number;
}

export function CompanyTreeView({
  companies,
  selectedCompany,
  onSelectCompany,
}: CompanyTreeViewProps) {
  const [nodePositions, setNodePositions] = useState<NodePosition[]>([]);

  // Calculate node positions to match the image layout
  useEffect(() => {
    const positions: NodePosition[] = [];

    if (companies.length === 0) {
      setNodePositions([]);
      return;
    }

    // Position Meta at the center
    const mainCompany = companies[0];
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2 - 50;

    positions.push({
      x: centerX,
      y: centerY,
      id: mainCompany.id,
      company: mainCompany,
      level: 0,
    });

    // Position children around Meta in specific positions to match the image
    if (mainCompany.children && mainCompany.children.length > 0) {
      const childPositions = [
        { x: centerX - 400, y: centerY + 200 }, // Facebook (bottom left)
        { x: centerX, y: centerY + 200 }, // Instagram (bottom center)
        { x: centerX + 400, y: centerY + 200 }, // Thread (bottom right)
      ];

      mainCompany.children.forEach((child, index) => {
        if (index < childPositions.length) {
          positions.push({
            x: childPositions[index].x,
            y: childPositions[index].y,
            id: child.id,
            company: child,
            level: 1,
          });
        }
      });
    }

    setNodePositions(positions);
  }, [companies]);

  const renderConnections = () => {
    const connections: JSX.Element[] = [];

    nodePositions.forEach((node) => {
      if (node.company.children) {
        node.company.children.forEach((child) => {
          const childNode = nodePositions.find((n) => n.id === child.id);
          if (childNode) {
            // Straight line connections like in the image
            const startX = node.x;
            const startY = node.y + 100; // Bottom of parent card
            const endX = childNode.x;
            const endY = childNode.y - 100; // Top of child card

            const pathId = `connection-${node.id}-${child.id}`;
            connections.push(
              <g key={pathId}>
                {/* Main straight line */}
                <line
                  x1={startX}
                  y1={startY}
                  x2={endX}
                  y2={endY}
                  stroke="rgba(34, 197, 94, 0.6)"
                  strokeWidth="2"
                  filter="url(#glow)"
                />

                {/* Connection dots along the line */}
                <circle
                  cx={startX}
                  cy={startY}
                  r="4"
                  fill="#22c55e"
                  className="animate-pulse"
                />
                <circle
                  cx={endX}
                  cy={endY}
                  r="4"
                  fill="#22c55e"
                  className="animate-pulse"
                />

                {/* Mid-point dots */}
                <circle
                  cx={startX + (endX - startX) * 0.25}
                  cy={startY + (endY - startY) * 0.25}
                  r="2"
                  fill="#22c55e"
                  className="animate-pulse"
                  style={{ animationDelay: "0.3s" }}
                />
                <circle
                  cx={startX + (endX - startX) * 0.5}
                  cy={startY + (endY - startY) * 0.5}
                  r="3"
                  fill="#22c55e"
                  className="animate-pulse"
                  style={{ animationDelay: "0.6s" }}
                />
                <circle
                  cx={startX + (endX - startX) * 0.75}
                  cy={startY + (endY - startY) * 0.75}
                  r="2"
                  fill="#22c55e"
                  className="animate-pulse"
                  style={{ animationDelay: "0.9s" }}
                />
              </g>
            );
          }
        });
      }
    });

    return connections;
  };

  const renderFloatingDots = () => {
    const dots: JSX.Element[] = [];
    for (let i = 0; i < 15; i++) {
      // Reduce number and make them more subtle
      dots.push(
        <div
          key={i}
          className="absolute w-0.5 h-0.5 bg-green-400/40 rounded-full animate-pulse"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animationDelay: `${Math.random() * 3}s`,
            animationDuration: `${3 + Math.random() * 2}s`,
          }}
        />
      );
    }
    return dots;
  };

  // Show loading state if no companies
  if (companies.length === 0) {
    return (
      <div
        className="min-h-screen w-full relative overflow-hidden flex items-center justify-center"
        style={{
          background: "linear-gradient(135deg, #FFFFFF 0%, #858585 100%)",
        }}
      >
        <div className="text-white text-xl">Loading companies...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full relative overflow-hidden bg-gradient-to-br from-gray-900 via-black to-gray-800">
      {/* Grid Background - Dot Matrix Style like in the image */}
      <div className="absolute inset-0">
        {/* Main dot grid pattern */}
        <div
          className="absolute inset-0 opacity-25"
          style={{
            backgroundImage: `
              radial-gradient(circle at 1px 1px, rgba(34, 197, 94, 0.4) 1px, transparent 0)
            `,
            backgroundSize: "40px 40px",
          }}
        />

        {/* Secondary smaller dots for detail */}
        <div
          className="absolute inset-0 opacity-15"
          style={{
            backgroundImage: `
              radial-gradient(circle at 1px 1px, rgba(34, 197, 94, 0.3) 0.5px, transparent 0)
            `,
            backgroundSize: "20px 20px",
          }}
        />

        {/* Subtle line grid overlay */}
        <div
          className="absolute inset-0 opacity-8"
          style={{
            backgroundImage: `
              linear-gradient(rgba(34, 197, 94, 0.1) 0.5px, transparent 0.5px),
              linear-gradient(90deg, rgba(34, 197, 94, 0.1) 0.5px, transparent 0.5px)
            `,
            backgroundSize: "40px 40px",
          }}
        />
      </div>

      {/* Floating dots */}
      {renderFloatingDots()}

      {/* SVG for connections */}
      <svg className="absolute inset-0 w-full h-full pointer-events-none">
        <defs>
          <filter id="glow">
            <feGaussianBlur stdDeviation="3" result="coloredBlur" />
            <feMerge>
              <feMergeNode in="coloredBlur" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>
        {renderConnections()}
      </svg>

      {/* Company Nodes */}
      <div className="relative w-full h-full">
        {nodePositions.map((node) => {
          const isSelected = selectedCompany === node.id;
          const isMainCompany = node.level === 0;

          return (
            <div
              key={node.id}
              className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer group"
              style={{
                left: node.x,
                top: node.y,
              }}
              onClick={() => onSelectCompany(node.id)}
            >
              {/* Large circular glow for Meta */}
              {isMainCompany && (
                <div className="absolute inset-0 w-80 h-80 -translate-x-1/2 -translate-y-1/2 left-1/2 top-1/2">
                  <div className="w-full h-full rounded-full bg-gradient-radial from-green-500/20 via-green-500/10 to-transparent animate-pulse" />
                  <div
                    className="absolute inset-4 rounded-full bg-gradient-radial from-green-400/15 via-green-400/5 to-transparent animate-pulse"
                    style={{ animationDelay: "0.5s" }}
                  />
                </div>
              )}

              {/* Card glow effect */}
              <div
                className={`absolute inset-0 rounded-3xl transition-all duration-300 ${
                  isMainCompany
                    ? "bg-green-400/30 shadow-[0_0_60px_rgba(34,197,94,0.4)]"
                    : isSelected
                    ? "bg-green-400/20 shadow-[0_0_40px_rgba(34,197,94,0.3)]"
                    : "group-hover:bg-green-400/10 group-hover:shadow-[0_0_30px_rgba(34,197,94,0.2)]"
                }`}
              />

              {/* Main card */}
              <div
                className={`relative bg-gray-900/80 backdrop-blur-md border rounded-3xl p-6 w-72 transition-all duration-300 ${
                  isMainCompany
                    ? "border-green-400/60 shadow-2xl"
                    : isSelected
                    ? "border-green-400/50 shadow-lg"
                    : "border-green-500/20 group-hover:border-green-400/40"
                }`}
              >
                {/* Circular plus button at top */}
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="w-8 h-8 rounded-full bg-green-500/20 border-2 border-green-400 flex items-center justify-center shadow-[0_0_20px_rgba(34,197,94,0.3)]">
                    <Plus className="w-4 h-4 text-green-400" />
                  </div>
                </div>

                {/* Header with icon */}
                <div className="flex items-center justify-center mb-4 mt-2">
                  <div className="w-12 h-12 rounded-2xl bg-green-500/20 flex items-center justify-center">
                    <Building2 className="w-6 h-6 text-green-400" />
                  </div>
                </div>

                {/* Company name */}
                <h3 className="text-white font-bold text-xl mb-3 text-center">
                  {node.company.name}
                </h3>

                {/* Description */}
                <p className="text-gray-300 text-sm mb-6 text-center leading-relaxed">
                  {node.company.description ||
                    "Automate refund processes with configurable policy enforcement."}
                </p>

                {/* Upload button */}
                <div className="flex justify-center">
                  <button className="flex items-center gap-2 text-green-400 text-sm hover:text-green-300 transition-colors bg-green-500/10 px-4 py-2 rounded-full border border-green-500/30 hover:border-green-400/50">
                    <Upload className="w-4 h-4" />
                    Upload
                  </button>
                </div>

                {/* Bottom circular plus button */}
                <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
                  <div className="w-8 h-8 rounded-full bg-green-500/20 border-2 border-green-400 flex items-center justify-center shadow-[0_0_20px_rgba(34,197,94,0.3)]">
                    <Plus className="w-4 h-4 text-green-400" />
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Right sidebar with company tree */}
      <div className="absolute top-4 right-4 w-80 bg-gray-900/90 backdrop-blur-md border border-green-500/30 rounded-2xl p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-lg bg-green-500/20 flex items-center justify-center">
              <Building2 className="w-4 h-4 text-green-400" />
            </div>
            <h3 className="text-white font-semibold text-lg">Meta</h3>
          </div>
          <ChevronDown className="w-4 h-4 text-green-400" />
        </div>

        <div className="space-y-3">
          {companies.map((company) => (
            <div key={company.id}>
              {/* Parent company */}
              <div className="flex items-center gap-3 text-sm">
                <div className="w-4 h-4 rounded border border-green-500/40 bg-green-500/10 flex items-center justify-center">
                  <div className="w-2 h-2 rounded-full bg-green-400" />
                </div>
                <div className="w-2 h-2 rounded-full bg-green-400" />
                <span className="text-white font-medium">{company.name}</span>
              </div>

              {/* Children */}
              {company.children &&
                company.children.map((child, index) => (
                  <div
                    key={child.id}
                    className="flex items-center gap-3 text-sm ml-6 mt-2"
                  >
                    <div className="w-4 h-4 rounded border border-green-500/30 bg-transparent flex items-center justify-center">
                      {/* Empty checkbox for children */}
                    </div>
                    <div className="w-2 h-2 rounded-full bg-green-400/60" />
                    <span className="text-gray-300">{child.name}</span>
                  </div>
                ))}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
