"use client";
import React, { useRef, useEffect, useState, Suspense } from 'react';
import { Canvas, useFrame, useLoader } from '@react-three/fiber';
import { OrbitControls, useTexture } from '@react-three/drei';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';
import * as THREE from 'three';

interface BrainMeshProps {
  color?: string;
  emissiveIntensity?: number;
}

const BrainMesh: React.FC<BrainMeshProps> = ({
  color = "#10b981",
  emissiveIntensity = 0.1
}) => {
  const meshRef = useRef<THREE.Group>(null);

  // Use React Three Fiber's useLoader hook for better integration
  const brainObject = useLoader(OBJLoader, '/obj/freesurff.Obj');
  const texture = useTexture('/obj/brain.jpg');

  // Animate the brain rotation
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.005;
      meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.3) * 0.1;
    }
  });

  // Clone and prepare the brain object
  const brainClone = brainObject.clone();

  // Apply materials to all meshes in the object
  brainClone.traverse((child) => {
    if (child instanceof THREE.Mesh) {
      child.material = new THREE.MeshPhongMaterial({
        map: texture,
        color: color,
        transparent: true,
        opacity: 0.9,
        emissive: color,
        emissiveIntensity: emissiveIntensity,
        shininess: 100,
      });
    }
  });

  // Create wireframe version
  const wireframeClone = brainObject.clone();
  wireframeClone.traverse((child) => {
    if (child instanceof THREE.Mesh) {
      child.material = new THREE.MeshBasicMaterial({
        color: color,
        transparent: true,
        opacity: 0.2,
        wireframe: true,
      });
    }
  });

  return (
    <group ref={meshRef}>
      <primitive object={brainClone} scale={[1, 1, 1]} />
      <primitive object={wireframeClone} scale={[1.02, 1.02, 1.02]} />
    </group>
  );
};

interface BrainModelProps {
  className?: string;
  color?: string;
  emissiveIntensity?: number;
  enableControls?: boolean;
}

// Loading fallback component
const LoadingFallback = ({ color }: { color: string }) => (
  <mesh>
    <sphereGeometry args={[1, 32, 32]} />
    <meshPhongMaterial
      color={color}
      transparent
      opacity={0.4}
      emissive={color}
      emissiveIntensity={0.1}
    />
  </mesh>
);

export const BrainModel: React.FC<BrainModelProps> = ({
  className = "",
  color = "#10b981",
  emissiveIntensity = 0.1,
  enableControls = false
}) => {
  return (
    <div className={`w-full h-full ${className}`}>
      <Canvas
        camera={{ position: [0, 0, 8], fov: 45 }}
        gl={{ antialias: true, alpha: true }}
        style={{ background: 'transparent' }}
      >
        {/* Lighting setup similar to the original */}
        <ambientLight intensity={0.4} color="#404040" />
        <directionalLight
          position={[5, 5, 5]}
          intensity={0.6}
          castShadow
        />
        <pointLight
          position={[0, 0, 0]}
          intensity={0.8}
          color={color}
          distance={15}
        />

        {/* Brain model with Suspense */}
        <Suspense fallback={<LoadingFallback color={color} />}>
          <BrainMesh color={color} emissiveIntensity={emissiveIntensity} />
        </Suspense>

        {/* Optional orbit controls */}
        {enableControls && (
          <OrbitControls
            minDistance={5}
            maxDistance={15}
            rotateSpeed={0.5}
            maxPolarAngle={Math.PI / 2}
            enablePan={false}
          />
        )}
      </Canvas>
    </div>
  );
};

export default BrainModel;
