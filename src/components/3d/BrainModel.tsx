"use client";
import React, { useRef, useEffect, useState } from 'react';
import { Canvas, useFrame, useLoader } from '@react-three/fiber';
import { OrbitControls, useTexture } from '@react-three/drei';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';
import * as THREE from 'three';

interface BrainMeshProps {
  color?: string;
  emissiveIntensity?: number;
}

const BrainMesh: React.FC<BrainMeshProps> = ({ 
  color = "#10b981", 
  emissiveIntensity = 0.1 
}) => {
  const meshRef = useRef<THREE.Group>(null);
  const [brainGeometry, setBrainGeometry] = useState<THREE.BufferGeometry | null>(null);
  
  // Load the brain texture
  const texture = useTexture('/obj/brain.jpg');
  
  // Load the OBJ file
  useEffect(() => {
    const loader = new OBJLoader();
    loader.load(
      '/obj/freesurff.Obj',
      (object) => {
        // Extract geometry from the loaded object
        object.traverse((child) => {
          if (child instanceof THREE.Mesh && child.geometry) {
            setBrainGeometry(child.geometry);
          }
        });
      },
      (progress) => {
        console.log('Brain model loading progress:', (progress.loaded / progress.total) * 100 + '%');
      },
      (error) => {
        console.error('Error loading brain model:', error);
      }
    );
  }, []);

  // Animate the brain rotation
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.005;
      meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.3) * 0.1;
    }
  });

  if (!brainGeometry) {
    return (
      <mesh>
        <sphereGeometry args={[1, 32, 32]} />
        <meshPhongMaterial 
          color={color}
          transparent
          opacity={0.8}
          emissive={color}
          emissiveIntensity={emissiveIntensity}
        />
      </mesh>
    );
  }

  return (
    <group ref={meshRef}>
      <mesh geometry={brainGeometry}>
        <meshPhongMaterial
          map={texture}
          color={color}
          transparent
          opacity={0.9}
          emissive={color}
          emissiveIntensity={emissiveIntensity}
          shininess={100}
        />
      </mesh>
      
      {/* Add a subtle wireframe overlay */}
      <mesh geometry={brainGeometry} scale={[1.02, 1.02, 1.02]}>
        <meshBasicMaterial
          color={color}
          transparent
          opacity={0.2}
          wireframe
        />
      </mesh>
    </group>
  );
};

interface BrainModelProps {
  className?: string;
  color?: string;
  emissiveIntensity?: number;
  enableControls?: boolean;
}

export const BrainModel: React.FC<BrainModelProps> = ({
  className = "",
  color = "#10b981",
  emissiveIntensity = 0.1,
  enableControls = false
}) => {
  return (
    <div className={`w-full h-full ${className}`}>
      <Canvas
        camera={{ position: [0, 0, 8], fov: 45 }}
        gl={{ antialias: true, alpha: true }}
        style={{ background: 'transparent' }}
      >
        {/* Lighting setup similar to the original */}
        <ambientLight intensity={0.4} color="#404040" />
        <directionalLight
          position={[5, 5, 5]}
          intensity={0.6}
          castShadow
        />
        <pointLight
          position={[0, 0, 0]}
          intensity={0.8}
          color={color}
          distance={15}
        />
        
        {/* Brain model */}
        <BrainMesh color={color} emissiveIntensity={emissiveIntensity} />
        
        {/* Optional orbit controls */}
        {enableControls && (
          <OrbitControls
            minDistance={5}
            maxDistance={15}
            rotateSpeed={0.5}
            maxPolarAngle={Math.PI / 2}
            enablePan={false}
          />
        )}
      </Canvas>
    </div>
  );
};

export default BrainModel;
